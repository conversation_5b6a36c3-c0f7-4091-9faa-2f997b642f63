"use client"

import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation for Select components
declare module "@radix-ui/react-select" {
  interface SelectItemIndicatorProps {
    children?: React.ReactNode
  }
  interface SelectIconProps {
    children?: React.ReactNode
  }
  interface SelectViewportProps {
    children?: React.ReactNode
  }
}

const Select = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root>
>(({ ...props }, ref) => {
  const RootComponent = SelectPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <RootComponent data-slot="select" ref={ref} {...props} />
})
Select.displayName = "Select"

const SelectGroup = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Group>
>(({ ...props }, ref) => {
  const GroupComponent = SelectPrimitive.Group as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Group> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <GroupComponent data-slot="select-group" ref={ref} {...props} />
})
SelectGroup.displayName = "SelectGroup"

const SelectValue = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Value>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Value>
>(({ ...props }, ref) => {
  const ValueComponent = SelectPrimitive.Value as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Value> & {
      ref?: React.Ref<HTMLSpanElement>
    }
  >
  return <ValueComponent data-slot="select-value" ref={ref} {...props} />
})
SelectValue.displayName = "SelectValue"

const SelectTrigger = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    className?: string
    size?: "sm" | "default"
    children?: React.ReactNode
    id?: string
  }
>(({ className, size = "default", children, ...props }, ref) => {
  const TriggerComponent = SelectPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLButtonElement>
    }
  >

  const IconComponent = SelectPrimitive.Icon as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Icon> & {
      children?: React.ReactNode
      asChild?: boolean
    }
  >

  return (
    <TriggerComponent
      data-slot="select-trigger"
      data-size={size}
      ref={ref}
      className={cn(
        "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      {children}
      <IconComponent asChild>
        <ChevronDownIcon className="size-4 opacity-50" />
      </IconComponent>
    </TriggerComponent>
  )
})
SelectTrigger.displayName = "SelectTrigger"

interface SelectContentProps
  extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content> {
  className?: string
  children?: React.ReactNode
  position?: "popper" | "item-aligned"
}

const SelectContent = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Content>,
  SelectContentProps
>(({ className, children, position = "popper", ...props }, ref) => {
  const ContentComponent = SelectPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  const ViewportComponent = SelectPrimitive.Viewport as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Viewport> & {
      className?: string
      children?: React.ReactNode
    }
  >

  return (
    <SelectPrimitive.Portal>
      <ContentComponent
        data-slot="select-content"
        ref={ref}
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",
          position === "popper" &&
            "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
          className
        )}
        position={position}
        {...props}
      >
        <SelectScrollUpButton />
        <ViewportComponent
          className={cn(
            "p-1",
            position === "popper" &&
              "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"
          )}
        >
          {children}
        </ViewportComponent>
        <SelectScrollDownButton />
      </ContentComponent>
    </SelectPrimitive.Portal>
  )
})
SelectContent.displayName = "SelectContent"

const SelectLabel = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const LabelComponent = SelectPrimitive.Label as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <LabelComponent
      data-slot="select-label"
      ref={ref}
      className={cn("text-muted-foreground px-2 py-1.5 text-xs", className)}
      {...props}
    />
  )
})
SelectLabel.displayName = "SelectLabel"

interface SelectItemProps
  extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> {
  className?: string
  children?: React.ReactNode
}

const SelectItem = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Item>,
  SelectItemProps
>(({ className, children, ...props }, ref) => {
  const ItemComponent = SelectPrimitive.Item as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ItemComponent
      data-slot="select-item"
      ref={ref}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
        className
      )}
      {...props}
    >
      <span className="absolute right-2 flex size-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </SelectPrimitive.ItemIndicator>
      </span>
      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
    </ItemComponent>
  )
})
SelectItem.displayName = "SelectItem"

const SelectSeparator = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const SeparatorComponent = SelectPrimitive.Separator as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <SeparatorComponent
      data-slot="select-separator"
      ref={ref}
      className={cn("bg-border pointer-events-none -mx-1 my-1 h-px", className)}
      {...props}
    />
  )
})
SelectSeparator.displayName = "SelectSeparator"

const SelectScrollUpButton = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const ScrollUpButtonComponent = SelectPrimitive.ScrollUpButton as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ScrollUpButtonComponent
      data-slot="select-scroll-up-button"
      ref={ref}
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronUpIcon className="size-4" />
    </ScrollUpButtonComponent>
  )
})
SelectScrollUpButton.displayName = "SelectScrollUpButton"

const SelectScrollDownButton = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const ScrollDownButtonComponent = SelectPrimitive.ScrollDownButton as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ScrollDownButtonComponent
      data-slot="select-scroll-down-button"
      ref={ref}
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronDownIcon className="size-4" />
    </ScrollDownButtonComponent>
  )
})
SelectScrollDownButton.displayName = "SelectScrollDownButton"

export {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectScrollDownButton,
  SelectScrollUpButton,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
}
