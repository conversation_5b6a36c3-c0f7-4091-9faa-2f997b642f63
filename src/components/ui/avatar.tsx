"use client"

import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"
import { cva, type VariantProps } from "class-variance-authority"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { <PERSON>lt<PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"

function getInitials(name: string) {
  const words = name.trim().split(" ")
  const initials = words.map(word => word.charAt(0).toUpperCase())
  return initials.slice(0, 2).join("")
}

const Avatar = React.forwardRef<
  React.ComponentRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> & {
    children?: React.ReactNode
    className?: string
    style?: React.CSSProperties
  }
>(({ children, className, style, ...props }, ref) => {
  const RootComponent = AvatarPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> & {
      children?: React.ReactNode
      className?: string
      style?: React.CSSProperties
      ref?: React.Ref<HTMLSpanElement>
    }
  >

  return (
    <RootComponent
      data-slot="avatar"
      ref={ref}
      className={cn(
        "relative flex size-8 shrink-0 overflow-hidden rounded-full",
        className
      )}
      style={style}
      {...props}
    >
      {children}
    </RootComponent>
  )
})
Avatar.displayName = "Avatar"

const AvatarImage = React.forwardRef<
  React.ComponentRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const ImageComponent = AvatarPrimitive.Image as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image> & {
      className?: string
      ref?: React.Ref<HTMLImageElement>
    }
  >

  return (
    <ImageComponent
      data-slot="avatar-image"
      ref={ref}
      className={cn("aspect-square size-full", className)}
      {...props}
    />
  )
})
AvatarImage.displayName = AvatarPrimitive.Image.displayName

const AvatarFallback = React.forwardRef<
  React.ComponentRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const FallbackComponent = AvatarPrimitive.Fallback as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLSpanElement>
    }
  >

  return (
    <FallbackComponent
      data-slot="avatar-fallback"
      ref={ref}
      className={cn(
        "bg-muted flex size-full items-center justify-center rounded-full",
        className
      )}
      {...props}
    >
      {children}
    </FallbackComponent>
  )
})
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName

const avatarStackVariants = cva(
  "transition duration-300 hover:scale-105 hover:z-10",
  {
    variants: {
      size: {
        default: "h-10 w-10",
        sm: "h-9 w-9 text-sm",
        lg: "h-11 w-11",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

interface AvatarStackProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof avatarStackVariants> {
  avatars: { src?: string; alt: string; href?: string }[]
  avatarClassName?: string
  limit?: number
  onMoreButtonClick?: (event: React.MouseEvent<HTMLButtonElement>) => void
  className?: string
  children?: React.ReactNode
}

function AvatarStack({
  avatars,
  limit = 4,
  size,
  onMoreButtonClick,
  className,
  avatarClassName,
  ...props
}: AvatarStackProps) {
  const limitedAvatars = avatars.slice(0, limit)
  const remainingCount = avatars.length - limitedAvatars.length

  return (
    <div className={cn("flex", className)} {...props}>
      {limitedAvatars.slice(0, limit).map((avatar) => (
        <TooltipProvider
          key={`${avatar.alt}-${avatar.src}`}
          delayDuration={200}
        >
          <Tooltip>
            <TooltipTrigger className="-ms-1 -me-1">
              {avatar.href ? (
                <Link href={avatar.href}>
                  <Avatar
                    className={cn(
                      avatarStackVariants({ size }),
                      avatarClassName
                    )}
                  >
                    <AvatarImage
                      src={avatar.src}
                      className="border-2 border-background"
                    />
                    <AvatarFallback className="border-2 border-background">
                      {getInitials(avatar.alt)}
                    </AvatarFallback>
                  </Avatar>
                </Link>
              ) : (
                <Avatar
                  className={cn(avatarStackVariants({ size }), avatarClassName)}
                >
                  <AvatarImage
                    src={avatar.src}
                    className="border-2 border-background"
                  />
                  <AvatarFallback className="border-2 border-background">
                    {getInitials(avatar.alt)}
                  </AvatarFallback>
                </Avatar>
              )}
            </TooltipTrigger>
            <TooltipContent className="capitalize -me-[1.23rem]">
              <p>{avatar.alt}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}

      {/* Show "+N" button if avatars exceed the limit */}
      {remainingCount > 0 && (
        <button
          type="button"
          onClick={onMoreButtonClick}
          className="-ms-1 -me-1"
          aria-label="Show more"
        >
          <Avatar
            className={cn(avatarStackVariants({ size }), avatarClassName)}
          >
            <AvatarFallback className="border-2 border-background">
              +{remainingCount}
            </AvatarFallback>
          </Avatar>
        </button>
      )}
    </div>
  )
}

export { Avatar, AvatarImage, AvatarFallback, AvatarStack }
