"use client"

import * as React from "react"

import { cn } from "@/lib/utils"

interface TableProps {
  className?: string
  children?: React.ReactNode
}

const Table = React.forwardRef<HTMLTableElement, TableProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        data-slot="table-container"
        className="relative w-full overflow-x-auto"
      >
        <table
          data-slot="table"
          ref={ref}
          className={cn("w-full caption-bottom text-sm", className)}
          {...(props as any)}
        >
          {children}
        </table>
      </div>
    )
  }
)
Table.displayName = "Table"

interface TableHeaderProps {
  className?: string
  children?: React.ReactNode
}

const TableHeader = React.forwardRef<HTMLTableSectionElement, TableHeaderProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <thead
        data-slot="table-header"
        ref={ref}
        className={cn("[&_tr]:border-b", className)}
        {...props}
      >
        {children}
      </thead>
    )
  }
)
TableHeader.displayName = "TableHeader"

interface TableBodyProps {
  className?: string
  children?: React.ReactNode
}

const TableBody = React.forwardRef<HTMLTableSectionElement, TableBodyProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <tbody
        data-slot="table-body"
        ref={ref}
        className={cn("[&_tr:last-child]:border-0", className)}
        {...props}
      >
        {children}
      </tbody>
    )
  }
)
TableBody.displayName = "TableBody"

interface TableFooterProps {
  className?: string
  children?: React.ReactNode
}

const TableFooter = React.forwardRef<HTMLTableSectionElement, TableFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <tfoot
        data-slot="table-footer"
        ref={ref}
        className={cn(
          "bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",
          className
        )}
        {...props}
      >
        {children}
      </tfoot>
    )
  }
)
TableFooter.displayName = "TableFooter"

interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  className?: string
  children?: React.ReactNode
  onClick?: () => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLTableRowElement>) => void
  tabIndex?: number
}

const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <tr
        data-slot="table-row"
        ref={ref}
        className={cn(
          "hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",
          className
        )}
        {...props}
      >
        {children}
      </tr>
    )
  }
)
TableRow.displayName = "TableRow"

interface TableHeadProps {
  className?: string
  children?: React.ReactNode
}

const TableHead = React.forwardRef<HTMLTableHeaderCellElement, TableHeadProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <th
        data-slot="table-head"
        ref={ref}
        className={cn(
          "text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
          className
        )}
        {...(props as any)}
      >
        {children}
      </th>
    )
  }
)
TableHead.displayName = "TableHead"

interface TableCellProps extends React.TdHTMLAttributes<HTMLTableDataCellElement> {
  className?: string
  children?: React.ReactNode
}

const TableCell = React.forwardRef<HTMLTableDataCellElement, TableCellProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <td
        data-slot="table-cell"
        ref={ref}
        className={cn(
          "p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
          className
        )}
        {...(props as any)}
      >
        {children}
      </td>
    )
  }
)
TableCell.displayName = "TableCell"

interface TableCaptionProps {
  className?: string
  children?: React.ReactNode
}

const TableCaption = React.forwardRef<HTMLTableCaptionElement, TableCaptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <caption
        data-slot="table-caption"
        ref={ref}
        className={cn("text-muted-foreground mt-4 text-sm", className)}
        {...props}
      >
        {children}
      </caption>
    )
  }
)
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
