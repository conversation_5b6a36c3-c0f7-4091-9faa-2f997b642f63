/**
 * API Route: Verify 2FA setup and enable 2FA
 * Verifies TOTP token and enables 2FA for the user
 */

import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { Result, err, ok } from "@/lib/result"
import {
  decryptTotpSecret,
  encryptBackupCodes,
  verifyTwoFactor,
} from "@/lib/totp-service"

interface VerifySetupRequest {
  readonly token: string
}

/**
 * POST /api/auth/2fa/verify-setup
 * Verifies TOTP token and enables 2FA for the user
 */
export async function POST(request: NextRequest): Promise<Response> {
  // Parse request body
  let body: VerifySetupRequest
  const bodyText = await request.text()

  if (!bodyText) {
    return Response.json({ error: "Request body is required" }, { status: 400 })
  }

  const parseResult = (() => {
    const parsed = JSON.parse(bodyText) as VerifySetupRequest
    if (!parsed.token || typeof parsed.token !== "string") {
      return err("Token is required and must be a string")
    }
    return ok(parsed)
  })()

  if (!parseResult.success) {
    return Response.json({ error: parseResult.error }, { status: 400 })
  }

  body = parseResult.data

  // Get authenticated user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return Response.json({ error: "Authentication required" }, { status: 401 })
  }

  // Get user from database
  const userResult = await db.query.users.findFirst({
    where: eq(users.id, session.user.id),
    columns: {
      id: true,
      totpSecret: true,
      totpEnabled: true,
      totpBackupCodes: true,
    },
  })

  if (!userResult) {
    return Response.json({ error: "User not found" }, { status: 404 })
  }

  if (userResult.totpEnabled) {
    return Response.json(
      { error: "2FA is already enabled for this account" },
      { status: 400 }
    )
  }

  if (!userResult.totpSecret) {
    return Response.json(
      {
        error: "2FA setup not initiated. Please call /api/auth/2fa/setup first",
      },
      { status: 400 }
    )
  }

  // Decrypt the stored secret
  const decryptedSecretResult = decryptTotpSecret(userResult.totpSecret)
  if (!decryptedSecretResult.success) {
    return Response.json(
      { error: `Failed to decrypt secret: ${decryptedSecretResult.error}` },
      { status: 500 }
    )
  }

  // Get backup codes (currently stored as plaintext during setup)
  const backupCodes = (userResult.totpBackupCodes as string[]) || []

  // Verify the provided token
  const verificationResult = verifyTwoFactor(
    decryptedSecretResult.data,
    body.token,
    backupCodes
  )

  if (!verificationResult.success) {
    return Response.json(
      { error: `Verification failed: ${verificationResult.error}` },
      { status: 400 }
    )
  }

  if (!verificationResult.data.isValid) {
    return Response.json(
      { error: "Invalid verification code" },
      { status: 400 }
    )
  }

  // Encrypt backup codes for secure storage
  const encryptedBackupCodesResult = encryptBackupCodes(backupCodes)
  if (!encryptedBackupCodesResult.success) {
    return Response.json(
      {
        error: `Failed to encrypt backup codes: ${encryptedBackupCodesResult.error}`,
      },
      { status: 500 }
    )
  }

  // Enable 2FA and store encrypted backup codes
  const updateResult = await db
    .update(users)
    .set({
      totpEnabled: true,
      totpBackupCodes: [encryptedBackupCodesResult.data], // Store as encrypted string in array
      totpLastUsed: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(users.id, session.user.id))
    .returning({ id: users.id })
    .then(() => ok(true))
    .catch((error) => err(`Database update failed: ${error.message}`))

  if (!updateResult.success) {
    return Response.json({ error: updateResult.error }, { status: 500 })
  }

  return Response.json(
    {
      message: "2FA has been successfully enabled for your account",
      enabled: true,
    },
    { status: 200 }
  )
}
