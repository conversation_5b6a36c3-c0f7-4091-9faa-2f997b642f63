# Code Complexity Analysis: Examples for Simplification

Here are 10 examples of code snippets from the codebase that could potentially be simplified or refactored for better readability, maintainability, or efficiency without reducing functionality.

## 1. Verbose Statistics Calculation in Waitlist API

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/app/api/admin/matching/waitlist/route.ts`
*   **Location:** Inside the `GET` function, the `statistics.byReason` object construction.
*   **Issue:** The code filters the `waitlistedStudents` array multiple times (once for each reason) to get counts. This is verbose and potentially inefficient for large lists.
*   **Snippet:**
    ```typescript
    // ...
            byReason: {
              late_applicant: waitlistedStudents.filter(
                (s) => s.reason === "late_applicant"
              ).length,
              no_suitable_match: waitlistedStudents.filter(
                (s) => s.reason === "no_suitable_match"
              ).length,
              low_scores: waitlistedStudents.filter(
                (s) => s.reason === "low_scores"
              ).length,
              incomplete_application: waitlistedStudents.filter(
                (s) => s.reason === "incomplete_application"
              ).length,
            },
    // ...
    ```
*   **Suggestion:** Calculate counts in a single pass using `reduce` or a loop with a map to improve efficiency and reduce verbosity.
    ```typescript
    // Suggested approach
    const reasonCounts = waitlistedStudents.reduce((acc, student) => {
      const reason = student.reason || 'unknown'; // Handle undefined reason
      acc[reason] = (acc[reason] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // ...
            byReason: {
              late_applicant: reasonCounts['late_applicant'] || 0,
              no_suitable_match: reasonCounts['no_suitable_match'] || 0,
              low_scores: reasonCounts['low_scores'] || 0,
              incomplete_application: reasonCounts['incomplete_application'] || 0,
              // Potentially add unknown: reasonCounts['unknown'] || 0
            },
    // ...
    ```

## 2. Manual Object Mapping in Students API

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/app/api/admin/students/route.ts`
*   **Location:** Inside the `GET` function, mapping `studentData` to `formattedStudents`.
*   **Issue:** Lengthy, manual mapping of properties. While explicit, it can be verbose and error-prone if many properties are involved or if the source/target structures change frequently.
*   **Snippet:**
    ```typescript
    const formattedStudents = studentData.map((student) => {
      return {
        id: student.user.id,
        studentId: student.studentProfile.studentId,
        name: student.user.name,
        email: student.user.email,
        avatar: student.user.image,
        role: student.user.role,
        status: student.studentProfile.status,
        // ... potentially more properties from student.user, student.studentProfile, etc.
      };
    });
    ```
*   **Suggestion:** If this pattern is common or involves many properties, consider a dedicated helper function `mapStudentToFormattedStudent(student)` to encapsulate the mapping logic. For simpler cases, ensure effective use of destructuring and renaming if source and target mostly align.

## 3. Complex Function Signature & Logic in Scoring (Gale-Shapley)

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/lib/matching-algorithm/gale-shapley.ts`
*   **Location:** The `calculateScore` function.
*   **Issue:** The function signature `calculateScore(student: Student, item: Organization | FacultyProject, type: "organization" | "faculty", weights: ScoringWeights, allProjects: Project[], allFaculty: FacultyProfile[]): number` suggests a complex function with many dependencies. The body likely contains intricate logic.
*   **Suggestion:** Break down `calculateScore` into smaller, more focused scoring component functions (e.g., `scoreAreaPreference`, `scoreResearchInterest`, `scoreGPAAlignment`). These smaller functions would be easier to understand, test, and maintain. The main `calculateScore` would then compose these results.

## 4. Potentially Deeply Nested Logic in Gale-Shapley Algorithm

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/lib/matching-algorithm/gale-shapley.ts`
*   **Location:** Within the main `galeShapley` function (structure inferred from typical implementations).
*   **Issue:** The core loop of Gale-Shapley often involves iterating while there are free proposers, then iterating through their preferences, then checking an acceptor's current matches and preferences. This can lead to 3-4 levels of nesting (loops and conditionals).
*   **Suggestion:** Encapsulate parts of the inner logic into well-named helper functions to reduce nesting depth and improve readability. For example, `findNextProposal(proposer, preferences)`, `evaluateProposal(acceptor, currentMatches, newProposal)`.

## 5. Complex Progress/Status Updates in Training Service (Hypothetical)

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/lib/services/training-service.ts`
*   **Location:** Functions like `getTrainingModuleProgress` or `updateTrainingStatus` (names inferred, based on typical service responsibilities).
*   **Issue:** Such functions often involve fetching multiple related entities (e.g., modules, user progress on sub-items, completion criteria) and then applying business rules that can result in long conditional chains or complex state calculations.
*   **Suggestion:** If status update logic is complex (many states and transitions), consider using a state machine pattern. For progress calculations involving multiple components, break down calculations per sub-component and then aggregate, possibly using helper functions for each part.

## 6. Verbose JWT/Session Callback Logic in Authentication (Hypothetical)

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/lib/auth.ts`
*   **Location:** Inside `callbacks: { jwt: async ({ token, ... }) => {...}, session: async ({ session, token, ... }) => {...} }` in NextAuth options.
*   **Issue:** These callbacks can become very long and hard to follow if they handle fetching user roles, permissions from the database, enriching the token with extensive user details, and then mapping all of that to the session object.
*   **Suggestion:** Abstract parts of the logic into separate, well-defined asynchronous helper functions. For example: `enrichTokenWithUserDetails(token, userId)`, `fetchUserPermissions(userId)`, and `mapTokenToSession(session, enrichedToken)`.

## 7. Similar Update Blocks in Notification Status API

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/app/api/admin/notifications/status/route.ts`
*   **Location:** `POST` handler, blocks for updating `readIds` and `unreadIds`.
*   **Issue:** The logic for updating notifications to 'read' and 'unread' status is very similar, differing mainly in the values being set.
*   **Snippet:**
    ```typescript
    if (readIds && readIds.length > 0) {
      await tx
        .update(userNotifications)
        .set({ status: "read", readAt: new Date() })
        .where(and(inArray(userNotifications.id, readIds), eq(userNotifications.userId, userId)));
    }
    if (unreadIds && unreadIds.length > 0) {
      await tx
        .update(userNotifications)
        .set({ status: "unread", readAt: null })
        .where(and(inArray(userNotifications.id, unreadIds), eq(userNotifications.userId, userId)));
    }
    ```
*   **Suggestion:** Create a helper function like `updateNotificationStatusBatch(tx, ids, userId, status, readAtValue)` to encapsulate the common update logic and reduce duplication.

## 8. Conditional Filter Construction in Students API

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/app/api/admin/students/route.ts`
*   **Location:** `GET` handler, construction of `filterConditions` array.
*   **Issue:** A series of `if` statements is used to build up an array of filter conditions. If the number of optional filters grows significantly, this pattern can become verbose and harder to manage.
*   **Snippet:**
    ```typescript
    const filterConditions: SQL[] = [eq(users.role, "student")];
    if (searchTerm) {
      filterConditions.push(
        or(
          ilike(users.name, `%${searchTerm}%`),
          ilike(users.email, `%${searchTerm}%`),
          ilike(studentProfiles.studentId, `%${searchTerm}%`)
        )!
      );
    }
    if (status) {
      filterConditions.push(eq(studentProfiles.status, status as StudentStatus));
    }
    // ... potentially more filters for other query parameters ...
    ```
*   **Suggestion:** For a small number of filters, the current approach is acceptable. If it grows, consider a more structured approach, such as an array of filter builder functions or a configuration object that maps query parameters to filter-generating functions. This can make adding new filters cleaner.

## 9. Redefined Combined Types in Gale-Shapley Interfaces

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/lib/matching-algorithm/gale-shapley.ts`
*   **Location:** Interface definitions like `Student`, `Organization`, `FacultyProject`.
*   **Issue:** These interfaces redefine combined types like `profile: StudentProfile & { user: User }` inline. While explicit, if this specific combination (`StudentProfile & { user: User }`) is used in multiple places or if the base types (`StudentProfile`, `User`) change, it requires updates in several locations.
*   **Snippet:**
    ```typescript
    export interface Student {
      id: string;
      profile: StudentProfile & { user: User }; // Combined type
      // ...
    }

    export interface Organization {
      id: string;
      profile: OrganizationProfile & { user: User }; // Combined type
      // ...
    }
    ```
*   **Suggestion:** Define intermediate combined types if they are conceptually distinct and reused. For example:
    ```typescript
    type EnrichedStudentProfile = StudentProfile & { user: User };
    type EnrichedOrganizationProfile = OrganizationProfile & { user: User };

    export interface Student {
      id: string;
      profile: EnrichedStudentProfile;
      // ...
    }
    ```
    This is a minor point but can improve maintainability for complex, shared type structures.

## 10. Embedded Data Transformation Logic in Waitlist API

*   **File:** `/Users/<USER>/WebstormProjects/shadowland/src/app/api/admin/matching/waitlist/route.ts`
*   **Location:** `GET` handler, mapping `unmatchedStudentsWithDetails` to `waitlistedStudents`.
*   **Issue:** The logic to transform `unmatchedStudentsWithDetails` (which includes determining `priority` and `reason`) is embedded directly within the `.map()` callback. If this transformation logic is complex, it can reduce the readability of the mapping operation.
*   **Snippet:**
    ```typescript
    const waitlistedStudents = unmatchedStudentsWithDetails.map((student) => {
      let priority = 3; // Default priority
      let reason: WaitlistReason = "no_suitable_match";

      // ... (logic to determine priority and reason based on student properties) ...
      // This logic might involve several checks and conditions.

      return {
        id: student.studentProfile.id,
        studentId: student.studentProfile.studentId,
        name: student.user.name,
        email: student.user.email,
        // ... many more properties from student.user and student.studentProfile ...
        priority,
        reason,
        applicationDate: student.studentProfile.applicationDate,
      };
    });
    ```
*   **Suggestion:** If the logic for determining `priority`, `reason`, and mapping other fields is non-trivial, extract it into a separate function, e.g., `transformToWaitlistedStudent(studentData): WaitlistedStudentItem`. This makes the `.map()` call cleaner (`.map(transformToWaitlistedStudent)`) and centralizes the transformation logic.
